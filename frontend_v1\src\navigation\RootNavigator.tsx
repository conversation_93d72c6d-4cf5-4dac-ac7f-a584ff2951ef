/**
 * Root Navigator - Enhanced Main Navigation Controller
 *
 * Component Contract:
 * - Conditionally renders navigation based on authentication state
 * - Supports dual-role navigation (customer/provider)
 * - Integrates with Zustand auth store for state management
 * - Provides type-safe navigation throughout the app
 * - Handles authentication flow transitions with proper state reset
 * - Follows React Navigation v6+ patterns
 * - Ensures login success navigates to home screen (not popup)
 * - Implements smooth transitions and proper navigation stack management
 * - Uses React Navigation's built-in state management for navigation
 *
 * @version 3.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { createStackNavigator } from '@react-navigation/stack';
import React, { useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';

import { CUSTOMER_TEST_ACCOUNTS } from '../config/testAccounts';
import { CustomerOnboardingCarousel } from '../screens/onboarding/CustomerOnboardingCarousel';
import { ProviderOnboardingCarousel } from '../screens/onboarding/ProviderOnboardingCarousel';
import { RoleSelectionScreen } from '../screens/onboarding/RoleSelectionScreen';
import { WelcomeScreen } from '../screens/onboarding/WelcomeScreen';
// import { LazyRoleSelectionScreen, LazyCustomerOnboardingCarousel, LazyProviderOnboardingCarousel } from './LazyScreens';
import { useSafeAuthStore } from '../store/authSlice';

// Import navigation stacks directly to avoid lazy loading issues
import { AuthStack } from './AuthStack';
import { CustomerStack } from './CustomerStack';
import { ProviderStack } from './ProviderStack';
import type { RootStackParamList } from './types';

const Stack = createStackNavigator<RootStackParamList>();

// Storage key for tracking if user has seen onboarding
const ONBOARDING_COMPLETED_KEY = '@vierla_onboarding_completed';

export const RootNavigator: React.FC = () => {
  // Safely access auth store with comprehensive error handling
  const {
    isAuthenticated = false,
    userRole = null,
    status = 'idle',
    checkAuthStatus,
    validateToken,
    loginSuccess,
  } = useSafeAuthStore();

  const [showInitialization, setShowInitialization] = useState(true);
  const [showRoleSelection, setShowRoleSelection] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [selectedRole, setSelectedRole] = useState<
    'customer' | 'service_provider' | null
  >(null);
  const [isInitializing, setIsInitializing] = useState(true);

  // Initialize authentication state on startup
  React.useEffect(() => {
    const initializeAuth = async () => {
      console.log('🔄 Checking authentication status on startup');
      setIsInitializing(true);

      try {
        // Ensure auth store methods are available before calling them
        if (!checkAuthStatus || !validateToken) {
          console.warn('⚠️ Auth store methods not available yet, retrying...');
          setTimeout(initializeAuth, 100);
          return;
        }

        // Check if user has stored auth data
        await checkAuthStatus();

        // If authenticated, validate the token
        if (isAuthenticated) {
          const isValid = await validateToken();
          if (!isValid) {
            console.log('❌ Token validation failed, showing login');
          }
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
      } finally {
        console.log(
          '🔧 Auth initialization finally block - isAuthenticated:',
          isAuthenticated,
        );
        setIsInitializing(false);

        // Always show initialization screen for unauthenticated users
        // This provides a consistent welcome experience regardless of previous onboarding status
        if (!isAuthenticated) {
          console.log('🎯 Unauthenticated user detected, showing initialization screen');
          setShowInitialization(true);
        } else {
          console.log('🔧 User is authenticated, skipping initialization');
          setShowInitialization(false);
        }
      }
    };

    initializeAuth();
  }, []);

  // Development auto-login for testing (DISABLED to show proper flow)
  React.useEffect(() => {
    // Commented out auto-login to show initialization and authentication screens
    // if (__DEV__ && !isAuthenticated) {
    //   // Auto-login with test customer account for development
    //   const testAccount = CUSTOMER_TEST_ACCOUNTS[0];
    //   console.log('🧪 Development auto-login with test account:', testAccount.email);
    //   // Simulate successful login with mock token
    //   loginSuccess('dev-mock-token-' + Date.now(), 'customer');
    //   setShowInitialization(false);
    // }
  }, [isAuthenticated, loginSuccess]);

  // Show loading during initialization
  if (isInitializing) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        {/* Add loading spinner here if needed */}
      </View>
    );
  }

  // Show initialization screen for new users
  if (showInitialization && !isAuthenticated) {
    return (
      <WelcomeScreen
        onGetStarted={() => {
          setShowInitialization(false);
          setShowRoleSelection(true);
        }}
        onSignIn={() => {
          // Simply proceed to sign in without marking onboarding as completed
          // This allows users to see the initialization screen again if they're not authenticated
          console.log('🔑 User chose to sign in from welcome screen');
          setShowInitialization(false);
        }}
      />
    );
  }

  // Show role selection screen after initialization
  if (showRoleSelection) {
    return (
      <RoleSelectionScreen
        onRoleSelected={role => {
          setSelectedRole(role);
          setShowRoleSelection(false);
          setShowOnboarding(true);
        }}
        onBack={() => {
          setShowRoleSelection(false);
          setShowInitialization(true);
        }}
      />
    );
  }

  // Show onboarding carousel based on selected role
  if (showOnboarding && selectedRole) {
    const handleOnboardingComplete = async () => {
      // Mark onboarding as completed
      try {
        await AsyncStorage.setItem(ONBOARDING_COMPLETED_KEY, 'true');
        console.log('✅ Onboarding marked as completed');
      } catch (error) {
        console.warn('Error saving onboarding completion status:', error);
      }
      setShowOnboarding(false);
    };

    if (selectedRole === 'customer') {
      return (
        <CustomerOnboardingCarousel
          onComplete={handleOnboardingComplete}
          onBack={() => {
            setShowOnboarding(false);
            setShowRoleSelection(true);
          }}
        />
      );
    } else {
      return (
        <ProviderOnboardingCarousel
          onComplete={handleOnboardingComplete}
          onBack={() => {
            setShowOnboarding(false);
            setShowRoleSelection(true);
          }}
        />
      );
    }
  }

  return (
    <View testID="root-stack" style={{ flex: 1 }}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false, // Prevent swipe back to auth screens
          cardStyleInterpolator: ({ current, layouts }) => {
            return {
              cardStyle: {
                transform: [
                  {
                    translateX: current.progress.interpolate({
                      inputRange: [0, 1],
                      outputRange: [layouts.screen.width, 0],
                    }),
                  },
                ],
              },
            };
          },
        }}>
        {/* Conditionally render screens based on authentication state */}
        {!isAuthenticated || !userRole ? (
          <Stack.Screen
            name="Auth"
            options={{
              animationTypeForReplace: 'pop',
            }}>
            {props => <AuthStack {...props} />}
          </Stack.Screen>
        ) : userRole === 'customer' ? (
          <Stack.Screen
            name="CustomerStack"
            options={{
              animationTypeForReplace: 'push',
            }}>
            {props => <CustomerStack {...props} />}
          </Stack.Screen>
        ) : (
          <Stack.Screen
            name="ProviderStack"
            options={{
              animationTypeForReplace: 'push',
            }}>
            {props => <ProviderStack {...props} />}
          </Stack.Screen>
        )}
      </Stack.Navigator>
    </View>
  );
};
