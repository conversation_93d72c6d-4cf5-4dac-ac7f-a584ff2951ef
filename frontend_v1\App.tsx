/**
 * <PERSON><PERSON><PERSON> App - Main Application Entry Point
 *
 * Component Contract:
 * - Provides navigation container for the entire app
 * - Integrates with role-based navigation system
 * - Handles authentication state routing
 * - Supports dual-role user experience (customer/provider)
 * - Follows React Navigation v6+ patterns
 * - Manages global navigation ref for programmatic navigation
 *
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

// CRITICAL: Pre-bundle fix MUST be absolutely first
import './src/utils/preBundleFix';
// CRITICAL: Emergency module loader MUST be first
import './src/utils/emergencyModuleLoader';
// CRITICAL: Ensure Hermes fixes are loaded (redundant import for safety)
import './src/utils/hermesErrorFix';
import './src/utils/hermesCompatibility';
import './src/utils/hermesModuleResolutionFix';
// CRITICAL: Runtime medium property fix for persistent errors
import './src/utils/runtimeMediumPropertyFix';

// CRITICAL: Initialize error monitoring early

import {
  <PERSON><PERSON><PERSON><PERSON>,
  DefaultTheme,
  DarkTheme,
} from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import React, { createRef, useEffect } from 'react';
import { View, Platform, StatusBar as RNStatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// CRITICAL: Import Hermes fixes AFTER React Native core modules
// Re-enabled to prevent Hermes runtime errors
import './src/utils/hermesErrorFix';

// CRITICAL: Import theme safety utilities to prevent theme-related crashes
import { initializeThemeSafety } from './src/utils/themeSafety';
import './src/utils/comprehensiveThemeFix';

// import { SentryErrorBoundary } from './src/components/error/SentryErrorBoundary';
// ErrorRecoveryProvider temporarily removed due to provider order issues
// Temporarily commented out to fix provider structure
// import { ResponsiveProvider } from './src/contexts/ResponsiveContext';

// Enhanced Error Handling
import { EnhancedToastProvider as ToastProvider } from './src/components/feedback/EnhancedToastSystem';
import {
  ConsistentHelpProvider,
  DocumentationProvider,
} from './src/components/help';
import OfflineIndicator from './src/components/OfflineIndicator';

// Analytics Service

// Performance Monitoring
import { PerformanceOptimizationProvider } from './src/contexts/PerformanceOptimizationContext';
import { monitoringIntegrationService } from './src/services/monitoringIntegrationService';

import { UndoProvider } from './src/components/providers';
import PWAInstallBanner from './src/components/PWAInstallBanner';
import ThemeErrorBoundary from './src/components/ThemeErrorBoundary';
import { ActionFeedbackProvider } from './src/components/ui/ActionFeedbackSystem';

import { GlobalFeedbackSystem } from './src/components/ui/GlobalFeedbackSystem';
import { ErrorPreventionProvider } from './src/components/ux/ErrorPreventionSystem';
import { UserControlProvider } from './src/components/ux/UserControlEnhancements';
import { CognitiveAccessibilityProvider } from './src/contexts/CognitiveAccessibilityContext';
import { HighContrastProvider } from './src/contexts/HighContrastContext';
import { I18nProvider } from './src/contexts/I18nContext';
import { MotorAccessibilityProvider } from './src/contexts/MotorAccessibilityContext';
import { ThemeProvider, useTheme } from './src/contexts/ThemeContext';
import { ConsistencyProvider } from './src/design-system/ConsistencyStandards';
import { RootNavigator } from './src/navigation/RootNavigator';
import { analyticsService } from './src/services/analyticsService';
import { cachingService } from './src/services/cachingService';
// Updated to use unified error handling system
import { unifiedErrorHandlingService } from './src/services/unifiedErrorHandling';
import { realTimeNotificationService } from './src/services/realTimeNotificationService';
import { useFeedbackStore } from './src/store/feedbackSlice';

// Global navigation ref for programmatic navigation
export const navigationRef = createRef<any>();

// Global Feedback System Integration Component
const GlobalFeedbackIntegration: React.FC = () => {
  const { messages, confirmationModal, dismissMessage, dismissConfirmation } =
    useFeedbackStore();

  return (
    <GlobalFeedbackSystem
      messages={messages}
      confirmationModal={confirmationModal}
      onDismissMessage={dismissMessage}
      onDismissModal={dismissConfirmation}
    />
  );
};

// Navigation Wrapper Component that uses theme context
const NavigationWrapper: React.FC = () => {
  const [isThemeReady, setIsThemeReady] = React.useState(false);

  // Wait for theme to be ready
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setIsThemeReady(true);
    }, 100); // Small delay to ensure theme is initialized

    return () => clearTimeout(timer);
  }, []);

  if (!isThemeReady) {
    return null; // Don't render navigation until theme is ready
  }

  return (
    <NavigationContainer ref={navigationRef}>
      <UserControlProvider>
        <RootNavigator />
        <GlobalFeedbackIntegration />
        <StatusBar style="auto" />
        <PWAInstallBanner />
        <OfflineIndicator showWhenOnline={true} />

      </UserControlProvider>
    </NavigationContainer>
  );
};

export default function App() {
  // Trigger bundler
  // Initialize theme safety utilities and monitoring on app start
  useEffect(() => {
    const appStartTime = Date.now();

    // Initialize unified error handling service
    console.log('🔍 App: Initializing unified error handling service');
    unifiedErrorHandlingService.initialize();
    console.log('✅ App: Unified error handling service initialized');

    // Initialize caching service for performance optimization
    console.log('🔍 App: Initializing caching service');
    cachingService.initialize();
    console.log('✅ App: Caching service initialized');

    // Initialize real-time notification service for WebSocket connections
    console.log('🔍 App: Initializing real-time notification service');
    // Note: Real-time service will auto-initialize when user authenticates
    // This ensures the service is ready for connection when auth token is available
    console.log(
      '✅ App: Real-time notification service ready for authentication',
    );

    console.log('🛡️ App: Initializing theme safety utilities');
    initializeThemeSafety();
    console.log('🛡️ App: Theme safety utilities initialized');

    // Initialize comprehensive monitoring
    const initializeMonitoring = async () => {
      try {
        console.log('📊 App: Initializing comprehensive monitoring');

        // Initialize monitoring integration service
        // TODO: Fix performance monitor service before enabling
        // await monitoringIntegrationService.initialize({
        //   enableAnalytics: true,
        //   enablePerformanceMonitoring: true,
        //   enableErrorReporting: true,
        //   enableCrashReporting: true,
        //   enableUserBehaviorTracking: true,
        //   enablePerformanceAlerts: true,
        //   enableRealTimeMonitoring: true,
        //   privacyCompliant: true,
        //   debugMode: __DEV__,
        // });

        // Track app startup performance
        const startupTime = Date.now() - appStartTime;
        console.log(`⏱️ App startup time: ${startupTime}ms`);

        // Track with monitoring service
        // TODO: Fix performance monitor service before enabling
        // await monitoringIntegrationService.trackAppStartup(appStartTime);

        // Track with analytics service
        await analyticsService.trackPerformance(
          'app_startup_time',
          startupTime,
          {
            timestamp: Date.now(),
            platform: 'react-native',
          },
        );

        console.log(
          '✅ App: Comprehensive monitoring initialized successfully',
        );
      } catch (error) {
        console.error('❌ App: Failed to initialize monitoring', error);
      }
    };

    initializeMonitoring();
  }, []);

  return (
    <SafeAreaProvider>
      {/* <SentryErrorBoundary level="critical"> */}
      <ThemeErrorBoundary>
        <View
          style={{
            flex: 1,
            paddingTop:
              Platform.OS === 'android' ? RNStatusBar.currentHeight || 0 : 44,
          }}>
          <I18nProvider>
            <ThemeProvider>
              <HighContrastProvider>
                <CognitiveAccessibilityProvider>
                  <MotorAccessibilityProvider>
                    <PerformanceOptimizationProvider
                      enableGlobalOptimization={true}
                      performanceBudget={{
                        maxRenderTime: 16, // 60fps
                        maxMemoryUsage: 100 * 1024 * 1024, // 100MB
                        maxBundleSize: 5 * 1024 * 1024, // 5MB
                        maxNetworkRequests: 10,
                        minFrameRate: 60,
                      }}
                      enableDashboard={__DEV__}>
                      <ToastProvider>
                        <ActionFeedbackProvider>
                          <UndoProvider>
                            <ErrorPreventionProvider>
                              <DocumentationProvider>
                                <ConsistentHelpProvider>
                                  <NavigationWrapper />
                                </ConsistentHelpProvider>
                              </DocumentationProvider>
                            </ErrorPreventionProvider>
                          </UndoProvider>
                        </ActionFeedbackProvider>
                      </ToastProvider>
                    </PerformanceOptimizationProvider>
                  </MotorAccessibilityProvider>
                </CognitiveAccessibilityProvider>
              </HighContrastProvider>
            </ThemeProvider>
          </I18nProvider>
        </View>
      </ThemeErrorBoundary>
      {/* </SentryErrorBoundary> */}
    </SafeAreaProvider>
  );
}
